# frozen_string_literal: true

class ApiPublic::V4::RealtyPriceGamesController < ApplicationController
  skip_before_action :verify_authenticity_token
  respond_to :json

  before_action :load_scoot # :load_dossier
  # before_action :load_price_estimate, only: %i[show]

  def single_listing_result
    @realty_game_listing = RealtyGameListing.find_by_uuid(
      params[:realty_game_listing_uuid]
    )
    game_slug = @realty_game_listing&.realty_game&.realty_game_slug

    session_id = params[:game_session_id]
    result = RealtyPunts::GameSessionResultsBoardCalculator.new(
      session_id, game_slug # params[:game_slug]
    ).call

    if result.success?
      render json: {
        rgl: @realty_game_listing.as_json(
          only: %w[
            uuid
          ],
          methods: %w[game_listing_display_title]
        ),
        results_calculation: result.data
      }
    else
      render json: { error: result.error_message }, status: :not_found
    end
  end

  def game_result_board
    session_id = params[:game_session_id]
    result = RealtyPunts::GameSessionResultsBoardCalculator.new(
      session_id, params[:game_slug]
    ).call

    if result.success?
      render json: result.data
    else
      render json: { error: result.error_message }, status: :not_found
    end
  end

  def game_result_calcs
    session_id = params[:game_session_id]
    # June 2025 - have to watch out for sql injection attack threat here..
    result = RealtyPunts::GameSessionResultsCalculator.new(session_id).call

    if result.success?
      render json: result.data
    else
      render json: { error: result.error_message }, status: :not_found
    end
  end

  def add_top_level_price_estimate
    # // don't like the name add_top_level_price_estimate but need to distinguish from
    # add_price_estimate
    # // which uses more restrictive assoc b/n scoot and games
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    # @realty_game = @scoot.all_realty_games.find_by(
    #   realty_game_slug: params[:realty_game_slug]
    # ) || @scoot.all_realty_games.find_by(
    #   game_default_locale: params[:realty_game_slug]
    # )

    # 8 july 2025 - decided I want it to be possible to play a
    # game from any scoot/subdomain
    @realty_game = RealtyGame.find_by(
      realty_game_slug: params[:realty_game_slug]
    ) || RealtyGame.find_by(
      game_default_locale: params[:realty_game_slug]
    )
    # hope to be able to remove find by game_default_locale when
    # get round to adding some sort of global_game_slug migration
    process_price_estimate_add
  end

  # below uses realty_games instead of all_realty_games
  def add_price_estimate
    ActsAsTenant.current_tenant = AgencyTenant.unique_tenant

    @realty_game = @scoot.realty_games.find_by(
      realty_game_slug: params[:realty_game_slug]
    )
    process_price_estimate_add
  end

  def process_price_estimate_add
    unless @realty_game
      render json: { error: 'Realty game not found' }, status: :not_found
      return
    end

    # Create or find GameSession instance
    game_session = find_or_create_game_session

    # Build guessed price with massaged parameters
    guessed_price_params = massage_price_estimate_params_for_guessed_price
    @guessed_price = @realty_game.guessed_prices.build(guessed_price_params)

    # Set required associations
    @guessed_price.agency_tenant_uuid = @scoot.agency_tenant_uuid
    @guessed_price.realty_game_uuid = @realty_game.uuid
    @guessed_price.realty_game_listing_uuid = @realty_game.realty_game_listings.find_by(
      listing_uuid: params[:price_estimate][:listing_uuid]
    ).uuid
    @guessed_price.guessed_price_in_ui_currency_cents = params[:price_estimate][:guessed_price_in_ui_currency_cents]
    # @guessed_price.score_for_guess = params[:price_estimate][:estimate_details][:game_score]

    if game_session
      # Set the game_session relationships
      # @guessed_price.game_session_id = game_session.uuid
      @guessed_price.game_session_uuid = game_session.uuid

      # Store additional details
      @guessed_price.guessed_price_details ||= {}
      # @guessed_price.guessed_price_details['game_session_id'] = game_session.uuid
      @guessed_price.guessed_price_details['session_guest_name'] = game_session.session_guest_name
    end

    if @guessed_price.save
      render json: {
        guessed_price: @guessed_price.as_json(
          only: %w[uuid game_session_id guessed_price_in_ui_currency_cents
                   guessed_price_amount_cents guessed_price_currency estimate_title
                   notes_on_guess estimator_name is_ai_estimate created_at],
          methods: %w[formatted_guessed_price]
        ),
        game_session: game_session&.as_json(
          only: %w[uuid session_guest_name session_guest_title created_at]
        ),
        message: 'Price guess created successfully'
      }, status: :created
    else
      render json: {
        errors: @guessed_price.errors.full_messages,
        message: 'Failed to create price guess'
      }, status: :unprocessable_entity
    end
  end

  def realty_game_summary
    # as of june 2025 - this is only used by housepriceguess domain
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)
    @guessed_price_validation = {
      max_percentage_above: 900,
      min_percentage_below: 90,
      messages: {
        too_high: 'Guess is way way too high',
        too_low: 'Guess is way too low',
        # too_high: 'Guess is more than 200% too high',
        # too_low: 'Guess is less than 90% of actual price',
        positive_number: 'Please enter a positive number'
      }
    }
    @realty_game = if params[:realty_game_slug]
                     @scoot.realty_games.find_by(
                       realty_game_slug: params[:realty_game_slug]
                     )
                   else
                     @scoot.realty_games.find_or_create_by(
                       realty_game_slug: 'regular-game'
                     )
                   end

    if !@realty_game.present? && incoming_subdomain == 'hpg-scoot'
      # 22 june 2025: below is a temporary workaround
      # so I can any given game from hpg.com
      # Have to be careful though as realty_game_slug is not
      # unique across the whole db
      @realty_game = RealtyGame.find_by(
        # realty_game_slug: params[:realty_game_slug]
        # another chapuzo - using game_default_locale as replacement for realty_game_slug as it is not
        # unique across the whole db
        game_default_locale: params[:realty_game_slug]
      )
    end

    @game_communities_details = game_communities_details(request.host)
    # expires_in HpgConfig::General::CACHE_EXPIRY_LOW, public: true
    render 'api_public/v4/realty_price_games/realty_game_summary'
  end

  # 29 june 2025 - about only diff b/n above and below atm is that
  # above limits indiv property details to minimum for metatags etc on fe
  # Primarily, no millions of listing photos..
  def realty_price_game_inputs
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)
    @guessed_price_validation = {
      max_percentage_above: 900,
      min_percentage_below: 90,
      messages: {
        too_high: 'Guess is way way too high',
        too_low: 'Guess is way too low',
        # too_high: 'Guess is more than 200% too high',
        # too_low: 'Guess is less than 90% of actual price',
        positive_number: 'Please enter a positive number'
      }
    }
    @realty_game = if params[:realty_game_slug]
                     @scoot.realty_games.find_by(
                       realty_game_slug: params[:realty_game_slug]
                     )
                   else
                     @scoot.realty_games.find_or_create_by(
                       realty_game_slug: 'regular-game'
                     )
                   end

    if !@realty_game.present? && incoming_subdomain == 'hpg-scoot'
      # 22 june 2025: below is a temporary workaround
      # so I can any given game from hpg.com
      # Have to be careful though as realty_game_slug is not
      # unique across the whole db
      @realty_game = RealtyGame.find_by(
        # realty_game_slug: params[:realty_game_slug]
        # another chapuzo - using game_default_locale as replacement for realty_game_slug as it is not
        # unique across the whole db
        game_default_locale: params[:realty_game_slug]
      )
    end

    @game_communities_details = game_communities_details(request.host)
    expires_in HpgConfig::General::CACHE_EXPIRY_LOW, public: true
    render 'api_public/v4/realty_price_games/realty_price_game_inputs'
  end

  # 18 june 2025 - for now only diff b/n above and below
  # is that listing_display_url is exposed below
  def realty_game_inputs_for_admin
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)
    @guessed_price_validation = {
      max_percentage_above: 900,
      min_percentage_below: 90,
      messages: {
        too_high: 'Guess is way way too high',
        too_low: 'Guess is way too low',
        positive_number: 'Please enter a positive number'
      }
    }
    # @realty_game = @scoot.realty_games.find_by(
    #   realty_game_slug: params[:realty_game_slug]
    # )

    # if @realty_game.blank?
    #   @realty_game = @scoot.all_realty_games.find_by(
    #     realty_game_slug: params[:realty_game_slug]
    #   )
    # end

    # This is me as admin - I want to find the game even if its not in this scoot:
    # @realty_game = RealtyGame.find_by(
    #   realty_game_slug: params[:realty_game_slug]
    # )

    @realty_game = RealtyGame.global_game_finder(params[:realty_game_slug])

    @game_communities_details = game_communities_details(request.host)
    return render json: { error: 'Realty game not found' }, status: :not_found if @realty_game.blank?

    render 'api_public/v4/realty_price_games/realty_game_inputs_for_admin'
  end

  private

  def massage_price_estimate_params_for_guessed_price
    params_hash = price_estimate_params.to_h

    # Map price estimate parameters to guessed price parameters
    {
      ui_currency: params_hash[:ui_currency],
      score_for_guess: params_hash[:score_for_guess],
      game_session_string: params_hash[:game_session_string] || params_hash[:game_session_id],
      guessed_price_amount_cents: params_hash[:estimated_price_cents],
      # guessed_price_in_cents: params_hash[:estimated_price_cents],
      guessed_price_currency: params_hash[:estimate_currency] || 'GBP',
      price_at_time_of_estimate_cents: params_hash[:price_at_time_of_estimate_cents],
      estimate_title: params_hash[:estimate_title],
      notes_on_guess: params_hash[:notes_on_guess],
      estimator_name: params_hash[:estimator_name],
      is_ai_estimate: params_hash[:is_ai_estimate],
      is_protected: params_hash[:is_protected],
      percentage_above_or_below: params_hash[:percentage_above_or_below],
      listing_uuid: params_hash[:listing_uuid],
      user_uuid: params_hash[:user_uuid],
      game_session_id: params_hash[:game_session_id],
      guessed_price_details: {
        session_guest_name: params_hash[:session_guest_name],
        session_guest_title: params_hash[:session_guest_title],
        original_estimate_details: params_hash[:estimate_details] || {}
      }
    }.compact
  end

  def game_communities_details(request_host)
    hosts = ['nuneaton.propertysquares.com', 'brum.propertysquares.com', 'brum-houses.propertysquares.com']
    relevant_games = hosts - [request_host]

    reddit_community = {
      url: 'https://www.reddit.com/r/propertysquares/',
      text: 'Join the discussion on reddit!'
    }

    reddit_community[:url] = 'https://www.reddit.com/r/nuneaton/' if request_host == 'nuneaton.propertysquares.com'

    {
      show: false,
      redditCommunity: reddit_community,
      relevantGames: relevant_games
    }
  end

  def load_scoot
    # @scoot = Scoot.find_by_uuid(params[:realty_scoot_uuid])
    incoming_subdomain = request.subdomain.presence || 'default'
    @scoot = Scoot.find_by(scoot_subdomain: incoming_subdomain.downcase)

    unless @scoot
      render json: { error: 'scoot not found' }, status: :not_found
      return false
    end

    true
  end

  # def load_dossier
  #   @scoot = RealtyDossier.find_by_uuid(params[:realty_dossier_uuid])

  #   unless @realty_dossier
  #     render json: { error: 'Dossier not found' }, status: :not_found
  #     return false
  #   end

  #   true
  # end

  # def load_price_estimate
  #   @price_estimate = @scoot.price_estimates.kept.find_by_uuid(params[:uuid])

  #   unless @price_estimate
  #     render json: { error: 'Price estimate not found' }, status: :not_found
  #     return false
  #   end

  #   true
  # end

  # Find or create a GameSession instance based on the provided parameters
  def find_or_create_game_session
    session_preferred_currency = params[:session_preferred_currency] || params.dig(:price_estimate, :ui_currency) || 'missing_spc'
    session_id = params[:game_session_id] || params.dig(:price_estimate, :game_session_id)
    guest_name = params[:session_guest_name] || params.dig(:price_estimate, :session_guest_name) || 'Anonymous Player'
    _guest_title = params[:session_guest_title] || params.dig(:price_estimate, :session_guest_title)

    # If session_id is provided, try to find existing session
    if session_id.present?
      existing_session = @realty_game.game_sessions.find_by(
        session_guest_title: session_id
      )
      return existing_session if existing_session
    end

    # Create new GameSession
    game_session = @realty_game.game_sessions.create!(
      session_guest_title: session_id.presence || SecureRandom.uuid,
      session_preferred_currency: session_preferred_currency,
      uuid: session_id.presence || SecureRandom.uuid,
      agency_tenant_uuid: @scoot.agency_tenant_uuid,
      main_scoot_uuid: @scoot.uuid,
      session_guest_name: guest_name,
      # session_guest_title: guest_title,
      game_session_details: {
        created_via: 'price_estimate_api',
        user_agent: request.user_agent,
        ip_address: request.remote_ip,
        created_at: Time.current
      }
    )

    # Track Ahoy event for new game session
    ahoy_res = ahoy.track(
      'game_session_started',
      {
        game_session_uuid: game_session.uuid,
        session_guest_name: game_session.session_guest_name,
        session_guest_title: game_session.session_guest_title,
        agency_tenant_uuid: game_session.agency_tenant_uuid,
        main_scoot_uuid: game_session.main_scoot_uuid,
        realty_game_uuid: @realty_game.uuid,
        realty_game_slug: @realty_game.realty_game_slug,
        created_at: game_session.created_at,
        user_agent: request.user_agent,
        ip_address: request.remote_ip,
        request_host: request.host,
        request_path: request.path,
        request_method: request.method,
        referer: request.referer,
        user_uuid: params[:user_uuid],
        created_via: 'price_estimate_api'
      }
    )
    puts "Ahoy res is #{ahoy_res}"

    if current_visit
      game_session.update(
        site_visitor_token: current_visit.visitor_token
      )
    end

    game_session
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error("Failed to create GameSession: #{e.message}")
    nil
  end

  def price_estimate_params
    params.require(:price_estimate).permit(
      :ui_currency, :guessed_price_in_ui_currency_cents,
      :score_for_guess,
      :estimated_price_cents, :game_session_id, :price_at_time_of_estimate_cents, :estimate_currency,
      :estimate_title, :notes_on_guess, :estimate_vicinity, :estimate_postal_code,
      :estimate_latitude_center, :estimate_longitude_center, :estimator_name,
      :is_ai_estimate, :is_for_sale_listing, :is_for_rental_listing, :is_protected,
      :percentage_above_or_below, :count_sold_transactions_shown, :listing_uuid,
      :user_uuid, :scoot_uuid, :session_guest_name, :session_guest_title, estimate_details: {}
    )
  end
end
