require 'administrate/base_dashboard'

class RealtyAssetDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    agency_tenant: Field::BelongsTo,
    agency_tenant_uuid: Field::String,
    agency_uuid: Field::String,
    area_unit: Field::Select.with_options(searchable: false, collection: lambda { |field|
                                                                           field.resource.class.send(field.attribute.to_s.pluralize).keys
                                                                         }),

    asset_photos: Field::HasMany,
    sold_transactions_count: Field::Number,
    categories: Field::String.with_options(searchable: false),
    city: Field::String,
    city_search_key: Field::String,
    cloned_from_uuid: Field::String,
    constructed_area: Field::Number.with_options(decimals: 2),
    count_bathrooms: Field::Number.with_options(decimals: 2),
    count_bedrooms: Field::Number,
    count_garages: Field::Number,
    count_toilets: Field::Number,
    country: Field::String,
    description: Field::Text,
    details: Field::String.with_options(searchable: false),
    discarded_at: Field::DateTime,
    energy_performance: Field::Number.with_options(decimals: 2),
    energy_rating: Field::Number,
    realty_asset_flags: Field::Number,
    floor: Field::String,
    google_place_id: Field::String,
    host_on_create: Field::String,
    import_url: Field::String,
    latitude: Field::Number.with_options(decimals: 2),
    longitude: Field::Number.with_options(decimals: 2),
    neighborhood: Field::String,
    neighborhood_search_key: Field::String,
    ra_photos_count: Field::Number,
    plot_area: Field::Number.with_options(decimals: 2),
    postal_code: Field::String,
    prop_origin_key: Field::String,
    prop_state_key: Field::String,
    prop_type_key: Field::String,
    province: Field::String,
    psq_visit_id: Field::Number,
    reference: Field::String,
    region: Field::String,
    related_urls: Field::String.with_options(searchable: false),
    rental_listings: Field::String, # Field::HasMany,
    sale_listings: Field::HasMany,
    site_visitor_token: Field::String,
    street_address: Field::String,
    street_name: Field::String,
    street_number: Field::String,
    title: Field::String,
    translations: Field::String.with_options(searchable: false),
    user_uuid: Field::String,
    uuid: Field::String,
    versions_count: Field::Number,
    year_construction: Field::Number,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    sold_transactions_count
    reference
    created_at
    sale_listings
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    sale_listings
    agency_tenant
    agency_tenant_uuid
    agency_uuid
    area_unit
    asset_photos
    categories
    city
    city_search_key
    cloned_from_uuid
    constructed_area
    count_bathrooms
    count_bedrooms
    count_garages
    count_toilets
    country
    description
    details
    discarded_at
    energy_performance
    energy_rating
    realty_asset_flags
    floor
    google_place_id
    host_on_create
    import_url
    latitude
    longitude
    neighborhood
    neighborhood_search_key
    ra_photos_count
    plot_area
    postal_code
    prop_origin_key
    prop_state_key
    prop_type_key
    province
    psq_visit_id
    reference
    region
    related_urls
    rental_listings
    site_visitor_token
    street_address
    street_name
    street_number
    title
    translations
    user_uuid
    uuid
    versions_count
    year_construction
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    agency_tenant
    agency_tenant_uuid
    agency_uuid
    area_unit
    asset_photos
    categories
    city
    city_search_key
    cloned_from_uuid
    constructed_area
    count_bathrooms
    count_bedrooms
    count_garages
    count_toilets
    country
    description
    details
    discarded_at
    energy_performance
    energy_rating
    realty_asset_flags
    floor
    google_place_id
    host_on_create
    import_url
    latitude
    longitude
    neighborhood
    neighborhood_search_key
    ra_photos_count
    plot_area
    postal_code
    prop_origin_key
    prop_state_key
    prop_type_key
    province
    psq_visit_id
    reference
    region
    related_urls
    rental_listings
    sale_listings
    site_visitor_token
    street_address
    street_name
    street_number
    title
    translations
    user_uuid
    uuid
    versions_count
    year_construction
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how realty assets are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(realty_asset)
  #   "RealtyAsset ##{realty_asset.id}"
  # end
end
