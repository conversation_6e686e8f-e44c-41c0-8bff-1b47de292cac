# == Schema Information
#
# Table name: game_sessions
#
#  id                          :bigint           not null, primary key
#  agency_tenant_uuid          :uuid
#  discarded_at                :datetime
#  game_session_details        :jsonb
#  game_session_flags          :integer          default(0), not null
#  guessed_prices_count        :integer          default(0)
#  is_protected                :boolean          default(FALSE), not null
#  llm_interaction_uuid        :uuid
#  main_realty_game_uuid       :uuid
#  main_scoot_uuid             :uuid
#  max_possible_score          :integer
#  one_off_price_guesses_count :integer          default(0)
#  performance_percentage      :decimal(5, 2)
#  performance_rating_color    :string
#  performance_rating_icon     :string
#  performance_rating_text     :string
#  results_calculated_at       :datetime
#  session_guest_name          :string
#  session_guest_title         :string
#  session_preferred_currency  :string           default("GBP"), not null
#  site_visitor_token          :string
#  total_score                 :integer
#  uuid                        :uuid             not null
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#
# Indexes
#
#  index_game_sessions_on_agency_tenant_uuid     (agency_tenant_uuid)
#  index_game_sessions_on_discarded_at           (discarded_at)
#  index_game_sessions_on_game_session_flags     (game_session_flags)
#  index_game_sessions_on_main_realty_game_uuid  (main_realty_game_uuid)
#  index_game_sessions_on_main_scoot_uuid        (main_scoot_uuid)
#  index_game_sessions_on_results_calculated_at  (results_calculated_at)
#  index_game_sessions_on_site_visitor_token     (site_visitor_token)
#  index_game_sessions_on_total_score            (total_score)
#  index_game_sessions_on_uuid                   (uuid) UNIQUE
#
class GameSession < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # Concerns
  include Discard::Model
  # has_paper_trail

  store_attribute :game_session_details, :performance_summary, :json, default: {}

  # Relationships
  belongs_to :agency_tenant, primary_key: 'uuid', foreign_key: 'agency_tenant_uuid'
  belongs_to :scoot, primary_key: 'uuid', foreign_key: 'main_scoot_uuid', optional: true
  belongs_to :realty_game, primary_key: 'uuid', foreign_key: 'main_realty_game_uuid', optional: true, class_name: 'RealtyGame'
  counter_culture :realty_game, column_name: 'game_sessions_count'
  has_many :realty_game_listings, through: :guessed_prices, source: :realty_game_listing
  # Realised after a while that game_sessions_count might be nonsense
  # as there can't be a direct relation between a game_session
  # Don't think there is a way to use counter_culture - might investigate later
  # belongs_to :realty_game_listing, primary_key: 'uuid', foreign_key: 'realty_game_uuid', optional: true, class_name: 'RealtyGame'
  # counter_culture :realty_game_listing, column_name: 'game_sessions_count'

  belongs_to :ahoy_visit, primary_key: 'visitor_token', foreign_key: 'site_visitor_token',
                          class_name: 'Ahoy::Visit', optional: true
  has_many :ahoy_events, through: :ahoy_visit, source: :events

  has_many :guessed_prices, primary_key: 'uuid', foreign_key: 'game_session_uuid', dependent: :destroy
  has_many :game_feedback_comms, primary_key: 'uuid', foreign_key: 'primary_assoc_uuid',
                                 class_name: 'Communications::GameFeedbackComm', dependent: :destroy

  # Price estimates relationship - handles both direct UUID and estimate_details lookup
  def price_estimates
    # Find estimates where game_session_id matches this session's UUID
    direct_estimates = PriceEstimate.where(game_session_id: uuid)

    # Also find estimates where estimate_details contains this session's UUID
    details_estimates = PriceEstimate.where("estimate_details->>'game_session_id' = ?", uuid)

    # Combine and return unique estimates
    PriceEstimate.where(id: (direct_estimates.pluck(:id) + details_estimates.pluck(:id)).uniq)
  end

  def game_player_nickname
    session_guest_name.split.map(&:capitalize).join(' ')
  end

  # Validations
  validates :uuid, presence: true, uniqueness: true
  validates :agency_tenant_uuid, presence: true

  # Callbacks
  before_validation :ensure_uuid

  # Instance methods
  def total_estimates_count
    price_estimates.count
  end

  def total_guessed_prices_count
    guessed_prices.count
  end

  def session_summary
    {
      uuid: uuid,
      guest_name: session_guest_name,
      guest_title: session_guest_title,
      estimates_count: total_estimates_count,
      guessed_prices_count: guessed_prices_count,
      created_at: created_at,
      game_title: realty_game&.game_title,
      results_calculated: results_calculated?,
      performance_summary: performance_summary
    }
  end

  # Check if results have been calculated and saved
  def results_calculated?
    results_calculated_at.present?
  end

  # Get performance summary from saved results
  def performance_summary
    return nil unless results_calculated?

    {
      total_score: total_score,
      max_possible_score: max_possible_score,
      performance_percentage: performance_percentage,
      rating: {
        text: performance_rating_text,
        icon: performance_rating_icon,
        color: performance_rating_color
      }
    }
  end

  # Save calculated results to the database
  def save_calculated_results!(player_results, comparison_summary)
    performance_rating = player_results[:performance_rating]
    total_score_value = player_results[:total_score]
    max_possible_score_value = player_results[:max_possible_score]

    update!(
      total_score: total_score_value,
      max_possible_score: max_possible_score_value,
      performance_percentage: max_possible_score_value > 0 ? (total_score_value.to_f / max_possible_score_value * 100).round(2) : 0,
      performance_rating_text: performance_rating[:rating],
      performance_rating_icon: performance_rating[:icon],
      performance_rating_color: performance_rating[:color],
      results_calculated_at: Time.current
    )

    # Also update statistics for each realty game listing
    update_listing_statistics!(comparison_summary)
  end

  private

  def ensure_uuid
    self.uuid ||= SecureRandom.uuid
  end

  # Update statistics for realty game listings based on comparison summary
  def update_listing_statistics!(comparison_summary)
    return if comparison_summary.blank?

    # Get unique listing UUIDs from guessed prices in this session
    listing_uuids = guessed_prices.pluck(:listing_uuid).compact.uniq

    listing_uuids.each do |listing_uuid|
      # Find the realty game listing
      realty_game_listing = RealtyGameListing.find_by(listing_uuid: listing_uuid)
      next unless realty_game_listing

      # Calculate statistics for this listing across all guessed prices
      all_guesses = GuessedPrice.where(listing_uuid: listing_uuid)
      next if all_guesses.empty?

      guess_amounts = all_guesses.pluck(:guessed_price_amount_cents).compact
      next if guess_amounts.empty?

      # Update the listing with calculated statistics
      realty_game_listing.update!(
        average_guess_cents: guess_amounts.sum / guess_amounts.count,
        highest_guess_cents: guess_amounts.max,
        lowest_guess_cents: guess_amounts.min,
        total_guesses_count: guess_amounts.count,
        statistics_updated_at: Time.current
      )
    end
  end
end
