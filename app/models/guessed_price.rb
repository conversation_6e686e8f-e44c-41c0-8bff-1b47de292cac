# == Schema Information
#
# Table name: guessed_prices
#
#  id                                 :bigint           not null, primary key
#  agency_tenant_uuid                 :uuid
#  discarded_at                       :datetime
#  estimate_title                     :string
#  estimator_name                     :string
#  extra_uuid                         :uuid
#  game_session_string                :string
#  game_session_uuid                  :uuid
#  guessed_price_amount_cents         :bigint           default(0), not null
#  guessed_price_currency             :string           default("GBP"), not null
#  guessed_price_details              :jsonb
#  guessed_price_flags                :integer          default(0), not null
#  guessed_price_in_ui_currency_cents :bigint           default(0), not null
#  is_ai_estimate                     :boolean          default(FALSE), not null
#  is_protected                       :boolean          default(FALSE), not null
#  listing_uuid                       :uuid
#  notes_on_guess                     :text
#  percentage_above_or_below          :integer          default(0), not null
#  price_at_time_of_estimate_cents    :bigint           default(0), not null
#  realty_game_listing_uuid           :uuid
#  realty_game_uuid                   :uuid
#  score_for_guess                    :integer          default(0), not null
#  source_currency                    :string           default("GBP"), not null
#  ui_currency                        :string           default("GBP"), not null
#  user_uuid                          :uuid
#  uuid                               :uuid             not null
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  game_session_id                    :string
#
# Indexes
#
#  index_guessed_prices_on_agency_tenant_uuid        (agency_tenant_uuid)
#  index_guessed_prices_on_discarded_at              (discarded_at)
#  index_guessed_prices_on_guessed_price_flags       (guessed_price_flags)
#  index_guessed_prices_on_realty_game_listing_uuid  (realty_game_listing_uuid)
#  index_guessed_prices_on_realty_game_uuid          (realty_game_uuid)
#  index_guessed_prices_on_user_uuid                 (user_uuid)
#  index_guessed_prices_on_uuid                      (uuid) UNIQUE
#
class GuessedPrice < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)

  # Concerns
  include Discard::Model
  # has_paper_trail

  # Relationships
  belongs_to :agency_tenant, primary_key: 'uuid', foreign_key: 'agency_tenant_uuid'
  belongs_to :realty_game_listing, primary_key: 'uuid', foreign_key: 'realty_game_listing_uuid', optional: true
  counter_culture :realty_game_listing, column_name: 'guessed_prices_count'
  belongs_to :game_session, primary_key: 'uuid', foreign_key: 'game_session_uuid', optional: true
  counter_culture :game_session, column_name: 'guessed_prices_count'
  belongs_to :realty_game, primary_key: 'uuid', foreign_key: 'realty_game_uuid', optional: true
  has_one :scoot, through: :realty_game
  counter_culture :realty_game, column_name: 'guessed_prices_count'
  belongs_to :user, primary_key: 'uuid', foreign_key: 'user_uuid', optional: true, class_name: 'Pwb::User'

  # Monetize price fields
  monetize :guessed_price_amount_cents, with_model_currency: :guessed_price_currency
  monetize :price_at_time_of_estimate_cents, with_model_currency: :guessed_price_currency

  # Validations
  validates :uuid, presence: true, uniqueness: true
  validates :agency_tenant_uuid, presence: true
  validates :guessed_price_amount_cents, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :guessed_price_currency, presence: true

  # Scopes
  scope :ai_estimates, -> { where(is_ai_estimate: true) }
  scope :protected_estimates, -> { where(is_protected: true) }
  scope :by_estimator, ->(name) { where(estimator_name: name) }

  # Callbacks
  before_validation :ensure_uuid
  before_validation :set_default_currency

  # Instance methods
  def formatted_guessed_price
    return 'N/A' if guessed_price_amount_cents.zero?

    guessed_price_amount.format
  end

  def source_listing_currency
    realty_game_listing&.source_listing_currency
  end

  def source_listing_display_url
    realty_game_listing&.listing_display_url
  end

  def vendor_specific_url(vendor_name)
    realty_game_listing&.listing&.vendor_specific_url(vendor_name)
  end

  def formatted_price_at_time_of_estimate
    return 'N/A' if price_at_time_of_estimate_cents.zero?

    price_at_time_of_estimate.format
  end

  def estimate_type
    return 'AI Estimate' if is_ai_estimate?
    return 'Professional Estimate' if estimator_name.present?

    'User Estimate'
  end

  def accuracy_indicator
    return 'N/A' if percentage_above_or_below.zero?

    if percentage_above_or_below.positive?
      "+#{percentage_above_or_below}%"
    else
      "#{percentage_above_or_below}%"
    end
  end

  private

  def ensure_uuid
    self.uuid ||= SecureRandom.uuid
  end

  def set_default_currency
    self.guessed_price_currency ||= 'GBP'
  end

  # # Polymorphic association for listings - can be either SaleListing or RentalListing
  # def listing
  #   return nil if listing_uuid.blank?

  #   if is_for_sale_listing?
  #     SaleListing.find_by(uuid: listing_uuid)
  #   elsif is_for_rental_listing?
  #     RentalListing.find_by(uuid: listing_uuid)
  #   end
  # end

  # # Monetize price fields
  # monetize :estimated_price_cents, with_model_currency: :estimate_currency
  # monetize :price_at_time_of_estimate_cents, with_model_currency: :estimate_currency
end
