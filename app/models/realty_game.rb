# frozen_string_literal: true

# == Schema Information
#
# Table name: realty_games
#
#  id                            :bigint           not null, primary key
#  agency_tenant_uuid            :uuid             not null
#  available_game_listings_count :integer          default(0)
#  discarded_at                  :datetime
#  game_area_details             :jsonb
#  game_bg_image_url             :string
#  game_default_country          :string
#  game_default_currency         :string           default("GBP"), not null
#  game_default_locale           :string
#  game_description              :string
#  game_end_at                   :datetime
#  game_global_slug              :string           default("")
#  game_jots_count               :integer          default(0)
#  game_listings_count           :integer          default(0)
#  game_notes                    :jsonb
#  game_primary_user_uuid        :uuid
#  game_rules                    :jsonb
#  game_sessions_count           :integer          default(0)
#  game_settings_flags           :integer          default(0), not null
#  game_source_portal            :integer          default("vendor_missing"), not null
#  game_start_at                 :datetime
#  game_starting_url             :string
#  game_title                    :string
#  game_type_flags               :integer          default(0), not null
#  guessed_prices_count          :integer          default(0)
#  is_one_off_game               :boolean          default(FALSE)
#  is_paid_game                  :boolean          default(FALSE)
#  is_public_listed_game         :boolean          default(FALSE)
#  one_off_mgmt_code             :string           default("")
#  ordered_listing_ids           :string           default([]), is an Array
#  realty_game_aasm_state        :string
#  realty_game_details           :jsonb
#  realty_game_flags             :integer          default(0), not null
#  realty_game_slug              :string
#  scoot_uuid                    :uuid
#  translations                  :jsonb
#  uuid                          :uuid
#  video_url_for_game            :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#
# Indexes
#
#  index_realty_games_on_discarded_at         (discarded_at)
#  index_realty_games_on_game_settings_flags  (game_settings_flags)
#  index_realty_games_on_game_type_flags      (game_type_flags)
#  index_realty_games_on_realty_game_flags    (realty_game_flags)
#  index_realty_games_on_realty_game_slug     (realty_game_slug)
#  index_realty_games_on_scoot_uuid           (scoot_uuid)
#  index_realty_games_on_uuid                 (uuid)
#
class RealtyGame < ApplicationRecord
  # Multi-tenancy setup
  acts_as_tenant :agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false

  include Discard::Model

  include FlagShihTzu
  has_flags 1 => :is_hidden_from_landing_page,
            2 => :is_price_guess_public,
            :column => 'game_settings_flags'

  # Enum for game_source_portal
  #
  enum game_source_portal: { vendor_missing: 0, buenavistahomes: 1,
                             costaspecialist: 2 }

  store_attribute :realty_game_details, :ordered_listing_ids, :json, default: []
  store_attribute :realty_game_details, :realty_game_leaderboard, :json, default: {}

  # Relationships
  has_many :realty_game_listings, primary_key: 'uuid', foreign_key: 'realty_game_uuid', dependent: :destroy
  # in realty_game_listing, r/n to here has this:
  #   counter_culture :realty_game, column_name: 'game_listings_count'

  # Gets the total count of properties in this game
  # @return [Integer] Total number of game listings
  # def total_properties_count
  #   realty_game_listings.kept.count
  # end

  has_many :game_sessions, primary_key: 'uuid', foreign_key: 'main_realty_game_uuid', dependent: :destroy
  has_many :guessed_prices, primary_key: 'uuid', foreign_key: 'realty_game_uuid', dependent: :destroy
  # has_many :price_estimates, primary_key: 'uuid', foreign_key: 'realty_game_uuid'

  belongs_to :agency_tenant, primary_key: 'uuid', foreign_key: 'agency_tenant_uuid'
  belongs_to :scoot, class_name: 'Scoot', foreign_key: 'scoot_uuid',
                     primary_key: :uuid, optional: true
  # counter_culture :scoot, column_name: 'realty_games_count'

  # Validations
  # validates :uuid, presence: true, uniqueness: true
  validates :agency_tenant_uuid, presence: true
  # validates :game_title, presence: true

  # Callbacks
  # before_validation :ensure_uuid
  before_validation :set_defaults

  # Instance methods

  # Adds a listing to this game from a URL
  # @param url [String] The property listing URL
  # @param portal [String, nil] Optional portal name (auto-detected if nil)
  # @return [RealtyGameListing] The created game listing
  def add_listing_from_url(url, portal = nil)
    Creators::RealtyGameListingCreator.new.create_game_listing_from_url(uuid, url, portal)
  end

  # Adds a listing to this game from pre-scraped content
  # @param url [String] The property listing URL
  # @param portal [String] The portal identifier
  # @param scrape_item_data [Hash] Pre-scraped content data
  # @return [RealtyGameListing] The created game listing
  def add_listing_from_pre_scraped_content(url, portal, scrape_item_data)
    Creators::RealtyGameListingCreator.new.create_game_listing_from_pre_scraped_content(uuid, url, portal, scrape_item_data)
  end

  def default_game_currency
    return realty_game_listings.first.source_listing_currency if realty_game_listings.present?

    'GBP'
  end

  # Gets all listings for this game grouped by type
  # @return [Hash] Hash with :sale and :rental keys containing arrays of listings
  def listings_by_type
    {
      sale: realty_game_listings.for_sale.includes(:listing),
      rental: realty_game_listings.for_rental.includes(:listing)
    }
  end

  # Ensures a persisted order for game listings and returns them with a position.
  #
  # This method performs several key functions:
  # 1. It fetches the current list of associated `realty_game_listings`.
  # 2. It compares this list against the persisted order in `ordered_listing_ids`.
  # 3. It reconciles the order by:
  #    - Removing IDs of listings that have been deleted.
  #    - Appending IDs of new listings to the end of the order.
  # 4. If the order has changed, it saves the updated order back to the database.
  # 5. It returns the `RealtyGameListing` objects, sorted according to the final order.
  # 6. Each returned listing object is decorated with a `listing_position_in_game` method
  #    that returns its 1-based position in the game.
  #
  # @return [Array<RealtyGameListing>] Sorted listings with an added position method.
  def ordered_game_listings
    # Eager load all kept listings to work with them in memory
    all_listings = realty_game_listings.kept
    current_listing_ids = all_listings.pluck(:id)

    # Retrieve the previously persisted order, defaulting to an empty array
    persisted_order = ordered_listing_ids || []

    # Reconcile the persisted order with the current reality
    # 1. Filter out IDs from deleted listings
    valid_ordered_ids = persisted_order & current_listing_ids
    # 2. Identify any new listings not yet in the order
    new_listing_ids = current_listing_ids - valid_ordered_ids
    # 3. Create the new, correct order by appending new listings
    updated_order = valid_ordered_ids + new_listing_ids

    # If the order has changed, persist it to the database
    if persisted_order != updated_order
      # Use update! to save the new order and raise an error on failure
      update!(ordered_listing_ids: updated_order)
    end

    # Return an empty array if there are no listings
    return [] if updated_order.empty?

    # Create a hash of listings keyed by ID for efficient sorting
    listings_by_id = all_listings.index_by(&:id)

    # Retrieve the listing objects in the correct order
    # .compact is a safeguard against any inconsistencies
    sorted_listings = listings_by_id.values_at(*updated_order).compact

    # Add the 'listing_position_in_game' virtual attribute to each listing
    sorted_listings.each_with_index do |listing, index|
      position = index + 1
      listing.define_singleton_method(:listing_position_in_game) { position }
    end

    sorted_listings
  end

  def uuid_for_game
    uuid
  end

  def global_game_slug
    # another chapuzo: use game_default_locale as a workaround
    # for a global game slug
    game_default_locale
  end

  def self.global_game_finder(finder_slug)
    # july 2025 - game_global_slug just got added in a migration
    # When I finally replace all uses of game_default_locale as chapuzo
    # will remove it from below
    RealtyGame.find_by(
      game_global_slug: finder_slug
    ) ||
      RealtyGame.find_by(
        realty_game_slug: finder_slug
      ) || RealtyGame.find_by(
        game_default_locale: finder_slug
      )
  end

  private

  # def ensure_uuid
  #   self.uuid ||= SecureRandom.uuid
  # end

  def set_defaults
    self.game_default_currency ||= 'GBP'
    self.game_default_country ||= 'UK'
    # self.game_default_locale ||= 'en'
  end
end
