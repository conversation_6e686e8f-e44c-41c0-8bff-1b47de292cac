# frozen_string_literal: true

require 'nokogiri'

module Pasarelas
  class ZooplaPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # Try to get data from script_json first (for LocalPlaywright scraper)
      next_data = nil
      doc = nil

      if @realty_scraped_item.script_json.present?
        begin
          # Handle case where script_json is already parsed JSON data
          if @realty_scraped_item.script_json.is_a?(Hash)
            next_data = @realty_scraped_item.script_json
          elsif @realty_scraped_item.script_json.is_a?(String)
            # Try to evaluate as Ruby hash first (safer than string manipulation)
            begin
              next_data = eval(@realty_scraped_item.script_json)
            rescue => eval_error
              Rails.logger.error("Failed to eval script_json as Ruby hash: #{eval_error.message}")
              # Try to parse as JSON with basic Ruby-to-JSON conversion
              json_string = @realty_scraped_item.script_json
              # Convert Ruby hash format to JSON format
              json_string = json_string.gsub(/=>\s*/, ': ')
                                      .gsub(/\bnil\b/, 'null')
                                      .gsub(/\btrue\b/, 'true')
                                      .gsub(/\bfalse\b/, 'false')
              next_data = JSON.parse(json_string)
            end
          end
        rescue JSON::ParserError => e
          Rails.logger.error("Failed to parse script_json as JSON: #{e.message}")
          # Fall back to HTML parsing
        rescue => e
          Rails.logger.error("Failed to process script_json: #{e.message}")
          # Fall back to HTML parsing
        end
      end

      # Fall back to HTML parsing if script_json didn't work
      if next_data.nil? && @realty_scraped_item.full_content_before_js.present?
        html_content = @realty_scraped_item.full_content_before_js
        return unless html_content.length > 1000

        begin
          # Parse the HTML to find the __NEXT_DATA__ script
          doc = Nokogiri::HTML(html_content)
          next_data_script = doc.at('script#__NEXT_DATA__')
          return unless next_data_script

          # Parse the JSON content from the script
          json_content = next_data_script.text
          next_data = JSON.parse(json_content)
        rescue JSON::ParserError => e
          Rails.logger.error("Failed to parse Zoopla JSON data from HTML: #{e.message}")
          return
        rescue Exception => e
          Rails.logger.error("Error processing Zoopla HTML data: #{e.message}")
          return
        end
      end

      return unless next_data

      begin
        # Extract property data from Zoopla structure
        listing_details = next_data.dig('props', 'pageProps', 'listingDetails')
        return unless listing_details

        # Map the data to our schema
        listing_data = map_property_to_listing_schema(listing_details)
        asset_data = map_property_to_asset_schema(listing_details)

        # Extract image URLs
        extracted_image_urls = extract_image_urls(listing_details, doc)

        # Save the extracted data to extra_scrape_item_details
        extra_details = @realty_scraped_item.extra_scrape_item_details || {}
        extra_details['extracted_asset_data'] = asset_data || {}
        extra_details['extracted_listing_data'] = listing_data || {}
        extra_details['extracted_image_urls'] = extracted_image_urls || []

        @realty_scraped_item.extra_scrape_item_details = extra_details
        @realty_scraped_item.save!

      rescue Exception => e
        Rails.logger.error("Error processing Zoopla property data: #{e.message}")
      end
    end

    private

    def extract_image_urls(listing_details, doc)
      image_urls = []

      # Primary: Get images from JSON data if available
      if listing_details['propertyImage'] && listing_details['propertyImage'].is_a?(Array)
        image_urls = listing_details['propertyImage'].map do |img|
          "https://lid.zoocdn.com/1024/768/#{img['filename']}"
        end.compact
      end

      # Fallback: CSS selectors (only if we have HTML doc)
      if image_urls.empty? && doc
        image_urls = doc.css('img[src*="zoocdn.com"]').map do |img|
          img['src'] || img['data-src']
        end.compact
      end

      image_urls
    end

    def map_property_to_asset_schema(listing_details)
      ad_targeting = listing_details['adTargeting'] || {}
      location = listing_details['location'] || {}
      coordinates = location['coordinates'] || {}
      counts = listing_details['counts'] || {}
      branch = listing_details['branch'] || {}

      {
        'title' => listing_details['title'],
        'categories' => extract_categories(listing_details),
        'city' => extract_city_from_address(listing_details['displayAddress']),
        'city_search_key' => extract_city_from_address(listing_details['displayAddress'])&.downcase&.gsub(/\s+/, '-') || '',
        'constructed_area' => listing_details.dig('floorArea', 'value')&.to_f || 0.0,
        'count_bathrooms' => counts['numBathrooms']&.to_f || 0.0,
        'count_bedrooms' => counts['numBedrooms'] || 0,
        'count_garages' => extract_garage_count(listing_details),
        'count_toilets' => 0, # Not available in Zoopla data
        'country' => map_country_code(ad_targeting['countryCode']),
        'description' => listing_details['detailedDescription'],
        'details' => extract_property_details(listing_details),
        'discarded_at' => nil,
        'energy_performance' => nil, # Not available in current data
        'energy_rating' => nil, # Not available in current data
        'floor' => nil, # Not directly available
        'has_rental_listings' => listing_details['section'] == 'to-rent',
        'has_sale_listings' => listing_details['section'] == 'for-sale' || listing_details['section'] == 'overseas',
        'has_sold_transactions' => false,
        'host_on_create' => 'zoopla',
        'is_ai_generated_realty_asset' => false,
        'latitude' => coordinates['latitude'],
        'longitude' => coordinates['longitude'],
        'neighborhood' => nil,
        'neighborhood_search_key' => '',
        'plot_area' => 0.0, # Not available in Zoopla data
        'postal_code' => location['postalCode'],
        'prop_state_key' => map_listing_condition(ad_targeting['listingCondition']),
        'prop_type_key' => listing_details['propertyType']&.downcase&.gsub(/\s+/, '-') || '',
        'province' => extract_province_from_address(listing_details['displayAddress']),
        'ra_photos_count' => listing_details['propertyImage']&.size || 0,
        'realty_asset_flags' => 0,
        'realty_asset_tags' => extract_tags(listing_details),
        'reference' => listing_details['listingId'],
        'region' => extract_region_from_address(listing_details['displayAddress']),
        'rental_listings_count' => listing_details['section'] == 'to-rent' ? 1 : 0,
        'sale_listings_count' => (listing_details['section'] == 'for-sale' || listing_details['section'] == 'overseas') ? 1 : 0,
        'sold_transactions_count' => 0,
        'street_address' => listing_details['displayAddress'],
        'street_number' => nil, # Not directly available
        'year_construction' => 0 # Not available in Zoopla data
      }
    end

    def map_property_to_listing_schema(listing_details)
      pricing = listing_details['pricing'] || {}
      ad_targeting = listing_details['adTargeting'] || {}
      counts = listing_details['counts'] || {}

      # Extract price in cents
      price_cents = extract_price_in_cents(pricing, ad_targeting)

      {
        'title' => listing_details['title'],
        'description' => listing_details['detailedDescription'],
        'archived' => false,
        'commission_cents' => 0,
        'commission_currency' => ad_targeting['currencyCode'] || 'GBP',
        'currency' => ad_targeting['currencyCode'] || 'GBP',
        'design_style' => nil,
        'details_of_rooms' => extract_room_details(listing_details),
        'discarded_at' => nil,
        'extra_sale_details' => extract_extra_sale_details(listing_details),
        'furnished' => extract_furnished_state(listing_details),
        'hide_map' => false,
        'highlighted' => false,
        'host_on_create' => 'zoopla',
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => listing_details['listingId'],
        'listing_tags' => [],
        'main_video_url' => extract_video_url(listing_details),
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => price_cents,
        'price_sale_current_currency' => ad_targeting['currencyCode'] || 'GBP',
        'price_sale_original_cents' => price_cents,
        'price_sale_original_currency' => ad_targeting['currencyCode'] || 'GBP',
        'property_board_items_count' => 0,
        'publish_from' => listing_details['publishedOn'],
        'publish_till' => nil,
        'reference' => listing_details['listingId'],
        'related_urls' => {},
        'reserved' => false,
        'sale_listing_features' => extract_listing_features(listing_details),
        'sale_listing_flags' => 0,
        'sale_listing_gen_prompt' => nil,
        'service_charge_yearly_cents' => 0,
        'service_charge_yearly_currency' => ad_targeting['currencyCode'] || 'GBP',
        'sl_photos_count' => listing_details['propertyImage']&.size || 0,
        'visible' => true
      }
    end

    # Helper methods for data extraction and mapping
    def extract_categories(listing_details)
      features = listing_details.dig('features', 'bullets') || []
      features.map.with_index { |feature, index| { 'id' => index + 1, 'name' => feature } }
    end

    def extract_city_from_address(address)
      return nil unless address
      # Simple extraction - take the last part before country
      parts = address.split(',').map(&:strip)
      parts.length > 1 ? parts[-2] : parts.first
    end

    def extract_province_from_address(address)
      return nil unless address
      parts = address.split(',').map(&:strip)
      parts.length > 2 ? parts[-3] : nil
    end

    def extract_region_from_address(address)
      extract_province_from_address(address)
    end

    def extract_garage_count(listing_details)
      description = listing_details['detailedDescription'].to_s.downcase
      description.include?('garage') ? 1 : 0
    end

    def map_country_code(country_code)
      case country_code&.upcase
      when 'GB', 'UK'
        'United Kingdom'
      when 'CV'
        'Cape Verde'
      else
        country_code&.upcase
      end
    end

    def map_listing_condition(condition)
      case condition&.downcase
      when 'pre-owned', 'resale'
        'used'
      when 'new-build', 'new'
        'new'
      else
        'used'
      end
    end

    def extract_tags(listing_details)
      tags = []
      tags << 'overseas' if listing_details['section'] == 'overseas'
      tags << 'auction' if listing_details.dig('pricing', 'isAuction')
      tags.concat(listing_details['smartTags'] || [])
      tags
    end

    def extract_price_in_cents(pricing, ad_targeting)
      # Try to get price from ad_targeting first (more reliable)
      if ad_targeting['priceActual']
        (ad_targeting['priceActual'] * 100).to_i
      elsif pricing['label']
        # Extract numeric value from price label
        price_str = pricing['label'].gsub(/[^\d.]/, '')
        price_str.present? ? (price_str.to_f * 100).to_i : 0
      else
        0
      end
    end

    def extract_room_details(listing_details)
      details = {}
      if counts = listing_details['counts']
        details['bedrooms'] = counts['numBedrooms'] if counts['numBedrooms']
        details['bathrooms'] = counts['numBathrooms'] if counts['numBathrooms']
        details['living_rooms'] = counts['numLivingRooms'] if counts['numLivingRooms']
      end
      details
    end

    def extract_extra_sale_details(listing_details)
      details = {}
      details['section'] = listing_details['section'] if listing_details['section']
      details['category'] = listing_details['category'] if listing_details['category']
      details['uuid'] = listing_details['uuid'] if listing_details['uuid']
      details
    end

    def extract_furnished_state(listing_details)
      furnished_state = listing_details.dig('features', 'flags', 'furnishedState')
      furnished_state == 'furnished'
    end

    def extract_video_url(listing_details)
      listing_details.dig('embeddedContent', 'videos', 0, 'url')
    end

    def extract_listing_features(listing_details)
      features = {}
      if bullets = listing_details.dig('features', 'bullets')
        bullets.each_with_index do |feature, index|
          features[index.to_s] = feature
        end
      end
      features
    end

    def extract_property_details(listing_details)
      details = {}
      details['floor_area'] = listing_details['floorArea'] if listing_details['floorArea']
      details['features'] = listing_details['features'] if listing_details['features']
      details['branch'] = listing_details['branch'] if listing_details['branch']
      details
    end

    def asset_attributes
      %w[title categories city city_search_key constructed_area count_bathrooms count_bedrooms count_garages count_toilets
         country description details discarded_at energy_performance energy_rating floor has_rental_listings has_sale_listings has_sold_transactions host_on_create latitude longitude neighborhood neighborhood_search_key plot_area postal_code prop_origin_key prop_state_key prop_type_key province ra_photos_count realty_asset_flags realty_asset_tags reference region rental_listings_count sale_listings_count site_visitor_token sold_transactions_count street_address street_number year_construction]
    end

    def listing_attributes
      %w[
        title description archived commission_cents commission_currency currency design_style
        details_of_rooms discarded_at extra_sale_details furnished hide_map highlighted
        host_on_create is_ai_generated_listing listing_pages_count listing_slug listing_tags
        main_video_url obscure_map page_section_listings_count position_in_list
        price_sale_current_cents price_sale_current_currency price_sale_original_cents
        price_sale_original_currency property_board_items_count publish_from publish_till
        reference related_urls reserved sale_listing_features sale_listing_flags
        sale_listing_gen_prompt service_charge_yearly_cents service_charge_yearly_currency
        site_visitor_token sl_photos_count visible
      ]
    end
  end
end
