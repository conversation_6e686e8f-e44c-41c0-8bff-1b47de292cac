module RealtyPunts
  # Newer - this superceeds both GameResultsCalculator and GameSessionResultsCalculator
  # Service class to calculate game session results using proper GameSession instances
  class GameSessionResultsBoardCalculator
    # A simple class to hold the result structure
    Result = Struct.new(:success?, :data, :error_message, keyword_init: true)

    def initialize(game_session_id, realty_game_slug)
      @game_session_id = game_session_id
      @realty_game = RealtyGame.find_by(
        realty_game_slug: realty_game_slug,
      ) || RealtyGame.find_by(
        game_default_locale: realty_game_slug,
      )
      @game_session = find_game_session
      @player_guessed_prices = load_player_guessed_prices
    end

    def call
      return Result.new(success?: false, error_message: "Game results session not found.") unless @game_session
      return Result.new(success?: false, error_message: "Game results session has no guessed prices.") if @player_guessed_prices.empty?

      player_results = calculate_player_overall_performance
      comparison_summary = calculate_comparison_summary
      overall_ranking = calculate_overall_ranking

      # Save calculated results to database
      save_results_to_database!(player_results, comparison_summary)

      # Update the leaderboard for the realty game
      update_leaderboard_for_game!

      if @game_session.session_guest_name.blank? || @game_session.session_guest_name == "Anonymous Player"
        # ideally should set session_guest_name elsewhere
        # but this works for now
        @game_session.update(
          session_guest_name: @game_session.guessed_prices.last.estimator_name || "Anonymous Player",
          # session_guest_title: "Anonymous Player"
        )
      end

      Result.new(
        success?: true,
        data: {
          # all_game_sessions: @realty_game.game_sessions,
          # above for debugging
          leaderboard: @realty_game.realty_game_leaderboard,
          player_results: player_results,
          comparison_summary: comparison_summary,
          overall_ranking: overall_ranking,
          game_session: @game_session.as_json(
            only: %w[uuid session_guest_name
                     estimator_name session_guest_title created_at
                     results_calculated_at],
            methods: %w[total_guessed_prices_count game_player_nickname
                        performance_summary],
          ),
        },
      )
    end

    private

    # Find the GameSession instance by UUID
    def find_game_session
      # Try to find by UUID first (proper way)
      # session = GameSession.find_by(uuid: @game_session_id)
      # return session if session

      # Fallback: try to find by session_guest_title for backward compatibility
      # GameSession.find_by(session_guest_title: @game_session_id)
      @realty_game.game_sessions.find_by(session_guest_title: @game_session_id)
    end

    # Load guessed prices for this game session
    def load_player_guessed_prices
      return GuessedPrice.none unless @game_session

      # Get guessed prices using the proper relationship
      guessed_prices = @game_session.guessed_prices

      # Order by property_index if available, otherwise by created_at
      guessed_prices.order(
        Arel.sql("COALESCE((guessed_price_details->>'property_index')::integer, 0), created_at")
      )
    end

    def calculate_player_overall_performance
      total_score = @player_guessed_prices.sum { |gp| gp.score_for_guess&.to_i || 0 }
      # total_score = @player_guessed_prices.sum { |gp| gp.guessed_price_details&.dig('game_score')&.to_i || 0 }
      max_possible_score = @player_guessed_prices.count * 100 # Assuming 100 is the max score per question
      session_date = @game_session.created_at
      {
        total_score: total_score,
        max_possible_score: max_possible_score,
        performance_rating: get_performance_rating(total_score, max_possible_score),
        session_date: session_date,
        session_guest_name: @game_session.session_guest_name,
        session_guest_title: @game_session.session_guest_title,
        game_results: @player_guessed_prices.as_json(
          only: %w[guessed_price_in_ui_currency_cents
                   uuid ui_currency estimate_title
                   price_at_time_of_estimate_cents guessed_price_details
                   score_for_guess
                   game_session_string
                   game_session_uuid
                   guessed_price_amount_cents
                   estimate_text listing_uuid percentage_above_or_below created_at],
          methods: %w[formatted_guessed_price source_listing_currency],
          # below was causing errors:
          # include: { guessed_price_details: {} }
        ),
      }
    end

    def get_performance_rating(score, max_score)
      percentage = max_score > 0 ? (score.to_f / max_score * 100).round : 0

      case percentage
      when 90..100
        { rating: "Excellent!", icon: "military_tech", color: "positive" }
      when 70..89
        { rating: "Great Job!", icon: "emoji_events", color: "positive" }
      when 50..69
        { rating: "Good Effort", icon: "thumb_up", color: "info" }
      when 30..49
        { rating: "Not Bad", icon: "sentiment_satisfied", color: "warning" }
      else
        { rating: "Needs Improvement", icon: "sentiment_dissatisfied", color: "negative" }
      end
    end

    def calculate_comparison_summary
      @player_guessed_prices.map do |player_guessed_price|
        listing_uuid = player_guessed_price.listing_uuid
        # all_guessed_prices_for_property = GuessedPrice.where(listing_uuid: listing_uuid)
        all_guessed_prices_for_property = @realty_game.guessed_prices.where(listing_uuid: listing_uuid)

        # Skip if no other guessed prices are available for some reason
        next if all_guessed_prices_for_property.empty?

        # Calculate average guess
        total_guess_value = all_guessed_prices_for_property.sum(:guessed_price_amount_cents)
        average_guess_cents = total_guess_value / all_guessed_prices_for_property.count

        # Calculate player's ranking
        # sorted_guessed_prices = all_guessed_prices_for_property.sort_by { |gp| -(gp.guessed_price_details&.dig('game_score')&.to_i || 0) }
        sorted_guessed_prices = all_guessed_prices_for_property.sort_by { |gp| -(gp.score_for_guess&.to_i || 0) }
        rank = sorted_guessed_prices.find_index { |gp| gp.uuid == player_guessed_price.uuid } + 1

        result = {
          property_title: player_guessed_price.estimate_title,
          property_vicinity: player_guessed_price.guessed_price_details&.dig("estimate_vicinity"),
          your_guess_cents: player_guessed_price.guessed_price_amount_cents,
          your_guess_formatted: format_currency(player_guessed_price.guessed_price_amount_cents, player_guessed_price.guessed_price_currency),
          average_guess_cents: average_guess_cents,
          average_guess_formatted: format_currency(average_guess_cents, player_guessed_price.guessed_price_currency),
          actual_price_cents: player_guessed_price.price_at_time_of_estimate_cents,
          actual_price_formatted: format_currency(player_guessed_price.price_at_time_of_estimate_cents, player_guessed_price.guessed_price_currency),
          currency: player_guessed_price.guessed_price_currency,
          ranking: get_ranking_details(rank, all_guessed_prices_for_property.count),
        }

        prop_disp_url = player_guessed_price.source_listing_display_url
        gsp = @game_session.realty_game.game_source_portal
        prop_disp_url = player_guessed_price.vendor_specific_url(gsp) if ["test"].include?(gsp)
        # Add property_url if present - Note: GuessedPrice doesn't have direct scoot relationship, so we'll skip this for now
        result[:property_url] = prop_disp_url if player_guessed_price.scoot.should_show_out_links && prop_disp_url&.present?
        result
      end.compact
    end

    def get_ranking_details(rank, total_players)
      # percentage_rank = (rank.to_f / total_players * 100)
      # text, color = case percentage_rank
      #               when 0..5, 100.0 # Include 100.0 explicitly for top rank
      #                 ["Outstanding! You're in the top 5% of players!", 'deep-purple']
      #               when 5.01..10
      #                 ["Great work! You're in the top 10%!", 'positive']
      #               when 10.01..20
      #                 ['Excellent! You beat most players.', 'teal']
      #               when 20.01..35
      #                 ["Nice! You're well above average.", 'primary']
      #               when 35.01..50
      #                 ["Decent! You're in the top half.", 'info']
      #               when 50.01..70
      #                 ["You're in the lower half. Room to grow!", 'warning']
      #               when 70.01..90
      #                 ["Tough competition! You're in the bottom 25%.", 'orange']
      #               else
      #                 ['You're in the bottom 10%. Don't give up!', 'negative']
      #               end

      if total_players <= 10
        # Special cases for tiny groups
        text, color = case rank
          when 1
            ["You're top of the leaderboard!", "deep-purple"]
          when 2
            ["Nice try! Just behind the leader.", "primary"]
          else
            ["", "info"]
          end
      else
        percentile_from_top = ((total_players - rank).to_f / total_players) * 100

        text, color = case percentile_from_top
          when 95..100
            ["Outstanding! You're in the top 5% of players!", "deep-purple"]
          when 90...95
            ["Great work! You're in the top 10%!", "positive"]
          when 80...90
            ["Excellent! You beat most players.", "teal"]
          when 65...80
            ["Nice! You're well above average.", "primary"]
          when 50...65
            ["Decent! You're in the top half.", "info"]
          when 30...50
            ["You're in the lower half. Room to grow!", "warning"]
          when 10...30
            ["Tough competition! You're in the bottom 25%.", "orange"]
          else
            ["You're in the bottom 10%. Don't give up!", "negative"]
          end
      end
      # percentile_from_top = ((total_players - rank).to_f / total_players) * 100

      # text, color = case percentile_from_top
      #               when 95..100
      #                 ["Outstanding! You're in the top 5% of players!", 'deep-purple']
      #               when 90..95
      #                 ["Great work! You're in the top 10%!", 'positive']
      #               when 80..90
      #                 ['Excellent! You beat most players.', 'teal']
      #               when 65..80
      #                 ["Nice! You're well above average.", 'primary']
      #               when 50..65
      #                 ["Decent! You're in the top half.", 'info']
      #               when 30..50
      #                 ["You're in the lower half. Room to grow!", 'warning']
      #               when 10..30
      #                 ["Tough competition! You're in the bottom 25%.", 'orange']
      #               else
      #                 ['You're in the bottom 10%. Don't give up!', 'negative']
      #               end
      # text = 'Come back after more people have played to see how you really rank.' if total_players < 3

      {
        rank: rank,
        total_players: total_players,
        performance_text: text,
        color: color,
      }
    end

    def format_currency(cents, currency_code)
      # Using Money gem is recommended for robust currency handling
      # gem 'money'
      Money.from_cents(cents, currency_code).format(no_cents: true)

      # # Simple fallback formatter
      # amount = cents / 100.0
      # symbol = currency_code == 'GBP' ? '£' : '$' # Add more as needed
      # "#{symbol}#{'%.2f' % amount}"
    end

    # Save calculated results to the database
    def save_results_to_database!(player_results, comparison_summary)
      return unless @game_session

      # Only save if results haven't been calculated yet or if they're outdated
      # (e.g., if new guesses have been added since last calculation)
      last_guess_time = @player_guessed_prices.maximum(:created_at)
      return if @game_session.results_calculated_at.present? &&
                @game_session.results_calculated_at > last_guess_time

      # Save results to GameSession
      @game_session.save_calculated_results!(player_results, comparison_summary)

      Rails.logger.info "Saved game session results for #{@game_session.uuid}"
    rescue StandardError => e
      Rails.logger.error "Failed to save game session results: #{e.message}"
      # Don't fail the entire calculation if saving fails
    end

    # Calculates the overall ranking of the current session among all sessions for the same game
    def calculate_overall_ranking
      return nil unless @game_session && @realty_game

      # Get all sessions for this game with a total_score
      sessions = @realty_game.game_sessions.where.not(total_score: nil)
      return nil if sessions.empty?

      # Order by total_score descending, then by created_at ascending for tie-breaker
      ordered_sessions = sessions.order(total_score: :desc, created_at: :asc)
      # Find the rank (1-based)
      rank = ordered_sessions.pluck(:id).find_index(@game_session.id)
      rank = rank ? rank + 1 : nil

      {
        rank: rank,
        total_sessions: ordered_sessions.count,
      }
    end

    # Updates the realty_game_leaderboard attribute for the game
    def update_leaderboard_for_game!
      return unless @realty_game
      sessions = @realty_game.game_sessions.where.not(total_score: nil)
      leaderboard = sessions.order(total_score: :desc, created_at: :asc).limit(11).map do |session|
        {
          session_guest_name: session.session_guest_name,
          total_score: session.total_score,
          max_possible_score: session.max_possible_score,
          performance_percentage: session.performance_percentage,
          created_at: session.created_at,
          uuid: session.uuid,
        }
      end
      @realty_game.update(realty_game_leaderboard: leaderboard)
    end
  end
end
