json.cache! ['price_guess_inputs', @realty_game, @scoot, @guessed_price_validation, @game_communities_details], expires_in: 1.minute do
  if @realty_game
    json.realty_game_details do
      json.call(@realty_game,
                'uuid_for_game', 'game_global_slug',
                'default_game_currency', 'global_game_slug', 'game_bg_image_url',
                #                 "realty_game_slug": "regular-game",
                # "guessed_prices_count": 2,
                # "game_sessions_count": 2,
                # "game_jots_count": 0,
                # "game_listings_count": 1,
                'game_start_at', 'game_end_at',
                'game_title', 'game_description',
                'ordered_listing_ids')
    end
  end
  json.price_guess_inputs do
    if @realty_game

      # json.set! 'default_game_currency', @realty_game.default_game_currency
      # json.set! 'global_game_slug', @realty_game.global_game_slug
      # json.set! 'game_bg_image_url', @realty_game.game_bg_image_url
      # json.set! 'game_title', @realty_game.game_title || 'Property Price Challenge'
      # json.set! 'game_description', @realty_game.game_description || 'Property Price Challenge'
      json.game_listings do
        json.array! @realty_game.ordered_game_listings do |realty_game_listing|
          json.call(realty_game_listing,
                    'position_in_game', 'visible_in_game',
                    'guessed_prices_count', 'game_sessions_count',
                    'visible_photo_uuids', # 'ordered_photo_uuids',
                    'gl_vicinity_atr', 'gl_image_url_atr', 'gl_title_atr', 'gl_description_atr',
                    'listing_position_in_game', 'id', 'uuid')
          # json.set! 'realty_game_listing', realty_game_listing
          json.listing_details do
            json.set! 'title', realty_game_listing.sale_listing.title
            json.set! 'uuid', realty_game_listing.sale_listing.uuid
            # json.set! 'description', realty_game_listing.sale_listing.description
            json.set! 'visible', realty_game_listing.sale_listing.visible
            # json.partial! 'api_public/v4/realty_price_games/sale_listing_game_details', sale_listing: realty_game_listing.sale_listing
          end
        end
      end
    else
      json.set! 'game_listings', []
    end
    json.set! 'game_desc', 'Test your property valuation skills' # 'You will love it'
    json.set! 'should_show_out_links', @scoot.should_show_out_links
    # json.set! 'realty_games', @scoot.realty_games
    json.set! 'guessed_price_validation', @guessed_price_validation
    json.set! 'game_communities_details', @game_communities_details
  end
end
