Rails.application.routes.draw do
  get '/custom_analytics/data', to: 'custom_analytics#data'
  get 'flowchart', to: 'flowcharts#show'
  match '/terminal',
        to: Terminalwire::Rails::Thor.new(MainTerminal),
        via: %i[get connect]
  # Define the route for the Firebase action URL - for
  # email ver or pwd reset..
  get '__/auth/action', to: 'firebase_auth#action'

  # namespace :api do
  #   namespace :v1 do
  #     resources :generic_properties, only: %i[index show]
  #     resources :generic_property_photos, only: %i[index show]
  #     resources :generic_property_sections, only: %i[index show]
  #   end
  # end
  # 28 dec 2024 - after moving api/v1/generic_properties.json endpoint
  # from above to below, had to re-authenticate for it to work
  authenticated :user, ->(u) { u.admin? } do
    namespace :api do
      namespace :peek do
        resources :relevant_places, only: %i[index show]
      end
      namespace :v1 do
        resources :relevant_places, only: %i[index show]
        resources :generic_properties, only: %i[index show]
        resources :generic_property_photos, only: %i[index show]
        resources :generic_property_sections, only: %i[index show]
      end
    end
    # mount MissionControl::Jobs::Engine, at: '/jobs'
    mount Logster::Web => '/logs'

    namespace :superbee do
      resources :game_sessions
      resources :realty_game_listings
      resources :guessed_prices
      resources :realty_games
      resources :sale_listings
      resources :scoots
      resources :realty_scraped_items
      namespace :communications do
        resources :game_feedback_comms
        resources :game_property_feedback_comms
      end
      root to: 'guessed_prices#index'
    end

    namespace :superwiser do
      namespace :ahoy do
        resources :events
        resources :visits
      end
      # resources :ahoy_events
      resources :participants do
        member do
          post :sync
          get :analytics
        end
      end
      resources :price_estimates
      resources :contextual_records
      resources :scoots
      resources :dossier_jots
      resources :communications
      resources :realty_dossiers
      resources :dossier_assets
      resources :dossier_asset_parts
      resources :dossier_assets_comparisons
      resources :scrape_items
      resources :llm_interactions
      resources :llm_interaction_associations
      resources :realty_assets
      resources :sale_listings
      resources :realty_asset_photos

      # resources :composite_image_sources
      namespace :ai_insights do
        resources :composite_image_sources
      end
      resources :geo_clusters
      resources :dossier_info_sources
      resources :summary_listings
      resources :realty_search_queries
      resources :sold_transaction_epcs
      resources :epc_details
      resources :postcode_areas
      resources :sold_transactions
      resources :generic_properties
      resources :generic_property_sections
      resources :generic_property_photos
      resources :ad_hoc_data
      resources :agency_tenants
      resources :zac_active_storage_blobs
      resources :zac_active_storage_attachments
      resources :zac_active_storage_variant_records
      # namespace :active_storage do
      #   resources :blobs
      # end

      namespace :game_stats do
        get :overview
        get :player_activity
        get :guess_analysis
      end
      # namespace :active_storage, path: nil, as: nil do
      #   resources :blobs
      # end
      namespace :pwb do
        # scope path: :pwb do
        # namespace :pwb do
        # resources :props
        resources :users
        # end
      end

      # https://medo.propertywebbuilder.com/superwiser/agency_tenants
      # Apr 2025 - noticing now that I'm getting below error in logster when I browse to above
      # ActionView::Template::Error (undefined method `superwiser_user_path' for an instance of #<Class:0x00007cc086465320>)
      # adding below in the hope that it fixes errors like the above
      # resources :props
      # resources :users
      root to: 'ad_hoc_data#index'
    end
  end

  # end

  # mount Rswag::Ui::Engine => "/api-docs"
  # get "/api-docs/xswag" => "xboss#xswag"

  # mount Ahoy::Engine, at: '/ahoy'
  # Seems above not needed if Ahoy.api = true

  # mount Rswag::Api::Engine => "/api-docs"
  # mount AhoyCaptain::Engine => "/ahoy_captain"
  # mount MaintenanceTasks::Engine => "/h2c_tasks"

  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'
  devise_for :users, class_name: 'Pwb::User'
  get 'my_users/sign_in', to: 'my_users/sessions#new', as: :new_my_user_session
  post 'my_users/sign_in', to: 'my_users/sessions#create', as: :my_user_session
  get 'map', to: 'maps#show'

  # Participant Analytics Routes
  scope :participant_analytics, as: :participant_analytics do
    get '/', to: 'participant_analytics#index', as: :index
    get '/dashboard', to: 'participant_analytics#dashboard', as: :dashboard
    get '/engagement', to: 'participant_analytics#engagement_dashboard', as: :engagement_dashboard
    get '/traffic', to: 'participant_analytics#traffic_dashboard', as: :traffic_dashboard
    get '/behavior', to: 'participant_analytics#behavior_dashboard', as: :behavior_dashboard

    # API endpoints for chart data
    get '/api/overview', to: 'participant_analytics#overview', as: :overview
    get '/api/participants_over_time', to: 'participant_analytics#participants_over_time', as: :participants_over_time
    get '/api/visit_distribution', to: 'participant_analytics#visit_distribution', as: :visit_distribution
    get '/api/engagement_scores', to: 'participant_analytics#engagement_scores', as: :engagement_scores
    get '/api/behavior_categories', to: 'participant_analytics#behavior_categories', as: :behavior_categories
    get '/api/device_types', to: 'participant_analytics#device_types', as: :device_types
    get '/api/traffic_sources', to: 'participant_analytics#traffic_sources', as: :traffic_sources
    get '/api/geographic_distribution', to: 'participant_analytics#geographic_distribution', as: :geographic_distribution
    get '/api/visit_frequency', to: 'participant_analytics#visit_frequency', as: :visit_frequency
    get '/api/session_duration', to: 'participant_analytics#session_duration', as: :session_duration
    get '/api/cohort_analysis', to: 'participant_analytics#cohort_analysis', as: :cohort_analysis
    get '/api/top_participants', to: 'participant_analytics#top_participants', as: :top_participants
  end

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get 'up' => 'rails/health#show', as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"

  resources :house_prices, only: [:index]
  get 'epc_display' => 'house_prices#epc_display'

  # Dec 2024 - doesn't make sense calling this api_admin when its not
  # got any auth....
  namespace :api_admin do
    # Oct 2024 - puting package_code in parenthesis makes it optional
    scope path: '(/:package_code)' do
      # scope path: "(/:package_code)", constraints: { package_code: /htoc-sbd|htoc-gate|wlbl-sbd|wlbl-gate/ } do
      defaults format: :json do
        namespace :v1 do
          get '/tenants/provision', to: 'tenants#provision'
          # post '/login', to: 'users#login'
          # post '/register', to: 'users#register'
          # post '/frb_auth_off', to: 'guests_from_firebase#frb_auth_off'
          post '/htoc_auth_on', to: 'guests_from_firebase#htoc_auth_on'
          post '/htoc_auth_reg', to: 'guests_from_firebase#htoc_auth_reg'
          post '/frb_auth_on', to: 'guests_from_firebase#multi_tenant_frb_auth_on'
          post '/frb_auth_reg', to: 'guests_from_firebase#multi_tenant_frb_auth_reg'
        end
      end
    end
  end

  scope module: :pwb do
    get '/check_subdomain' => 'api_setup#check_subdomain'
    get '/demo/api_setup/current/web-setup.json' => 'api_setup#current'
    root to: 'marketing#index'
    resources :welcome, only: :index

    # authenticate :user do
    get '/propertysquares' => 'squares#vue'
    get '/propertysquares/*path' => 'squares#vue'
    get '/squares/:client_id' => 'squares#show_client'
    get '/squares/:client_id/:prop_id' => 'squares#show_prop'
    get '/admin' => 'admin_panel#show'
    get '/admin/*path' => 'admin_panel#show'
    get '/admin-1' => 'admin_panel#show_legacy_1'
    get '/admin-1/*path' => 'admin_panel#show_legacy_1'
    scope '(:locale)', locale: /#{I18n.available_locales.join('|')}/ do
      get '/admin' => 'admin_panel#show', as: 'admin_with_locale'
      get '/admin/*path' => 'admin_panel#show'
      get '/admin-1' => 'admin_panel#show_legacy_1', as: 'admin_with_locale_legacy'
      get '/admin-1/*path' => 'admin_panel#show_legacy_1'
    end
    get '/config' => 'config#show'
    get '/config/:params' => 'config#show'

    get '/v-admin' => 'admin_panel_vue#show'
    get '/v-admin/*path' => 'admin_panel_vue#show'
    # end

    get '/v-public' => 'vue_public#show'
    get '/v-public/*path' => 'vue_public#show'

    namespace :api do
      namespace :v1 do
        get '/reference_properties/list' => 'reference_properties#list'
        get '/reference_properties/show/:reference_property_uuid' => 'reference_properties#show'

        get '/sale_listings/populate/from_saved_data/:saved_slug' => 'sale_listings_populator#from_saved_data'
        get '/sale_listings/populate/from_bvh_ref/:bvh_ref' => 'sale_listings_populator#from_bvh_ref'
        get '/sale_listings/show/:sale_listing_uuid' => 'sale_listings#show'
        get '/sale_listings/list' => 'sale_listings#list'
        get '/purchase_evaluations/list' => 'purchase_evaluations#list'
        get '/purchase_evaluations/show/:pe_uuid' => 'purchase_evaluations#show'

        resources :props, only: %i[index show update] do
          member do
            patch :update_master_address
          end
        end
        # get "/cms/tag/:tag_name" => "cms#tag"
        get '/translations/list/:locale' => 'translations#list'

        # below gets FieldConfig values for a batch_key such as "person-titles"
        # and returns all the locale translations so an admin
        # can manage them..
        get '/translations/batch/:batch_key' => 'translations#get_by_batch'
        post '/translations' => 'translations#create_translation_value'

        post '/translations/create_for_locale' => 'translations#create_for_locale'
        put '/translations/:id/update_for_locale' => 'translations#update_for_locale'
        delete '/translations/:id' => 'translations#delete_translation_values'

        # put "tenant" => "agency#update_legacy"
        put '/master_address' => 'agency#update_master_address'

        get '/agency' => 'agency#show'
        put '/agency' => 'agency#update'
        put '/website' => 'website#update'
        get '/infos' => 'agency#infos'

        put '/pages' => 'page#update'
        put '/pages/page_part_visibility' => 'page#update_page_part_visibility'
        put '/pages/page_fragment' => 'page#save_page_fragment'
        get '/pages/:page_name' => 'page#show'

        # post '/page_fragments/photos/:page_id/:block_label' => 'page_fragments#set_photo'

        post '/pages/photos/:page_slug/:page_part_key/:block_label' => 'page#set_photo'
        # post '/cms-pages/photos/:page_id/:block_label' => 'cms_pages#set_photo'
        # jsonapi_resources :cms_pages

        get '/web-contents' => 'agency#infos'

        resources :lite_properties, only: %i[index]
        resources :realty_asset_photos, only: %i[index show]
        resources :properties, only: %i[index show create destroy]
        put '/properties/:id' => 'properties#update'
        # jsonapi_resources :lite_properties
        # jsonapi_resources :properties
        # # jsonapi_resources :clients
        # jsonapi_resources :web_contents
        resources :contacts

        get '/links' => 'links#index'
        put '/links' => 'links#bulk_update'

        get '/themes' => 'themes#index'
        get '/mls' => 'mls#index'
        get '/select_values' => 'select_values#by_field_names'

        # TODO: rename to update_features:
        post 'properties/update_extras' => 'properties#update_extras'

        delete 'properties/photos/:id' => 'properties#remove_photo'
        delete 'properties/photos/:id/:prop_id' => 'properties#remove_photo'
        post '/properties/bulk_create' => 'properties#bulk_create'
        post '/properties/:id/photo' => 'properties#add_photo'
        post '/properties/:id/photo_from_url' => 'properties#add_photo_from_url'
        put 'properties/:id/order_photos' => 'properties#order_photos'

        post 'properties/set_owner' => 'properties#set_owner'
        post 'properties/unset_owner' => 'properties#unset_owner'

        put '/web_contents/photos/:id/:content_tag' => 'web_contents#update_photo'
        # above is used by logo and about_me photos
        # where only one photo is allowed

        post '/web_contents/photo/:tag' => 'web_contents#create_content_with_photo'
        # above for carousel photos where I need to be able to
        # create content along with the photo
      end
    end
  end

  # # Nov 2024 - thought I needed to replicate
  # #   # /api_h2c/v1/purchase_evaluations/get_for_dash/1b423aec-ff6b-4e44-96ef-6fd9e2bfd414
  # # so I can experiment with legacy route
  # namespace :api_h2c do
  #   namespace :v1 do
  #     get '/purchase_evaluations/get_for_dash/:purchase_evaluation_uuid' => 'purchase_evaluations#get_for_dash' # , as: "single_page"
  #   end
  # end
  # # realised in the end that it was not necessary

  # authenticated :user, ->(u) { u.admin? } do
  # end

  # March 2025 to do add authentication for below
  # These routes are for the homestocompare AI listing assessments
  namespace :api_mgmt do
    namespace :v4 do
      scope '(:locale)', locale: /#{I18n.available_locales.join('|')}/ do
        scope path: :realty_games_mgmt do
          get '/clear_cache/:clear_cache_code' => 'realty_games_mgmt#clear_cache'
          get '/show_available_games/:sbdmn_name' => 'realty_games_mgmt#show_available_games'
          # post '/create_game' => 'realty_games_mgmt#create_game'
          post 'init_game_with_listing', to: 'realty_games_mgmt#init_game_with_listing'
          post 'add_listing_to_game', to: 'realty_games_mgmt#add_listing_to_game'
          # New endpoints for pre-scraped content
          post 'init_game_with_pre_scraped_listing', to: 'realty_games_mgmt#init_game_with_pre_scraped_listing'
          post 'add_pre_scraped_listing_to_game', to: 'realty_games_mgmt#add_pre_scraped_listing_to_game'
          # 4 july 2025 - New endpoints for pre-scraped and prepped listing content
          # below 2 will eventually replace above 2
          post 'init_game_with_prepped_listing_hash', to: 'game_maker_mgmt#init_game_with_prepped_listing_hash'
          post 'add_prepped_listing_to_game', to: 'game_maker_mgmt#add_prepped_listing_to_game'

          patch '/listing_visibility/:listing_uuid' => 'realty_games_mgmt#listing_visibility'
          patch '/photo_visibility/:photo_uuid' => 'realty_games_mgmt#photo_visibility'
        end

        # july 2025 - above is doing too much now. Adding below controller
        #  to offload some of above
        scope path: :scoot_games_mgmt do
          patch '/:game_uuid/listing_in_game_visibility/:realty_game_listing_uuid' => 'scoot_games_mgmt#listing_in_game_visibility'
        end

        scope path: :listing_in_game_mgmt do
          patch '/:realty_game_listing_uuid/update' => 'listing_in_game_mgmt#update'
        end

        scope path: :dossiers_mgmt do
          # get '/show_dossier' => 'dossiers_mgmt#show_dossier'
          post '/dossier_from_url' => 'dossiers_mgmt#dossier_from_url'
          put '/add_asset_from_url' => 'dossiers_mgmt#add_asset_from_url'
        end
        scope path: :realty_asset_photos do
          # put '/:realty_asset_photo_uuid/set_photo_visibility' => 'realty_asset_photos#set_photo_visibility'
          put '/:realty_asset_photo_uuid/discard_photo' => 'realty_asset_photos#discard_photo'
          put '/:realty_asset_photo_uuid/update_details' => 'realty_asset_photos#update_details'
          put '/:realty_asset_photo_uuid/set_photo_title' => 'realty_asset_photos#set_photo_title'
          put '/set_pics_order' => 'realty_asset_photos#set_pics_order'
        end
      end
    end
  end

  # May 2025 these routes will have a header with an access token
  namespace :api_guest do
    namespace :v4 do
      scope '(:locale)', locale: /#{I18n.available_locales.join('|')}/ do
        get    '/dossiers/:realty_dossier_uuid/neighbourhoods' => 'neighbourhoods#list'
        get    '/dossiers/:realty_dossier_uuid/neighbourhoods/:scoot_uuid' => 'neighbourhoods#show'

        # Dossier jots CRUD
        # get    '/scoots/:realty_dossier_uuid/jots'          => 'scoots#index'
        # post   '/scoots/:realty_dossier_uuid/jots'          => 'scoots#create'
        get    '/scoots/:scoot_uuid'    => 'scoots#show'
        put    '/scoots/:scoot_uuid'    => 'scoots#update'
        patch  '/scoots/:scoot_uuid'    => 'scoots#update'
        # delete '/scoots/:realty_dossier_uuid/jots/:uuid'    => 'scoots#destroy'

        # Dossier jots CRUD
        get    '/dossiers/:realty_dossier_uuid/jots'          => 'dossier_jots#index'
        post   '/dossiers/:realty_dossier_uuid/jots'          => 'dossier_jots#create'
        get    '/dossiers/:realty_dossier_uuid/jots/:uuid'    => 'dossier_jots#show'
        put    '/dossiers/:realty_dossier_uuid/jots/:uuid'    => 'dossier_jots#update'
        patch  '/dossiers/:realty_dossier_uuid/jots/:uuid'    => 'dossier_jots#update'
        delete '/dossiers/:realty_dossier_uuid/jots/:uuid'    => 'dossier_jots#destroy'

        # Dossier tasks CRUD
        get    '/dossiers/:realty_dossier_uuid/tasks'          => 'dossier_tasks#index'
        post   '/dossiers/:realty_dossier_uuid/tasks'          => 'dossier_tasks#create'
        get    '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#show'
        put    '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#update'
        patch  '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#update'
        delete '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#destroy'

        get '/dossiers/show/:realty_dossier_uuid' => 'dossiers#show'
        # get '/dossier_assets/show/:dossier_asset_id' => 'dossier_assets_comparisons#show_dossier_asset'
        get '/dossier_assets_comparisons/show/:comparison_uuid' => 'dossier_assets_comparisons#show'
      end
    end
  end
  # These routes are mainly for the homestocompare AI listing assessments
  namespace :api_public do
    namespace :v4 do
      scope '(:locale)', locale: /#{I18n.available_locales.join('|')}/ do
        get '/game_sale_listings/show/:sale_listing_uuid' => 'game_listings#show_for_sale'
        # 13 july 2025 - above was a bit dumb as it did not allow me to retrieve
        # realty_game_listing data around the sale_listing
        get '/game_sale_listings/show_rgl/:realty_game_listing_uuid' => 'game_listings#show_realty_game_listing'

        # get '/sale_listings/show/:sale_listing_uuid' => 'sale_listings#show'
        post '/realty_price_games/add_top_level_price_estimate/:realty_game_slug' => 'realty_price_games#add_top_level_price_estimate'
        post '/realty_price_games/add_price_estimate/:realty_game_slug' => 'realty_price_games#add_price_estimate'
        get '/realty_price_games/game_session/:game_session_id/game_result_calcs' => 'realty_price_games#game_result_calcs'
        # below will eventually replace above 30 june 2025:
        get '/game_result_board/:game_session_id/:game_slug' => 'realty_price_games#game_result_board'
        get '/single_listing_result/:game_session_id/:realty_game_listing_uuid' => 'realty_price_games#single_listing_result'

        get '/realty_game_inputs' => 'realty_price_games#realty_price_game_inputs'
        get '/realty_game_inputs/:realty_game_slug' => 'realty_price_games#realty_price_game_inputs'
        # realty_game_summary below is similar to above but far less verbose as images remove
        # as of june 2025 - this is only used by housepriceguess domain
        get '/realty_game_summary/:realty_game_slug' => 'realty_price_games#realty_game_summary'
        get '/realty_game_inputs_for_admin/:realty_game_slug' => 'realty_price_games#realty_game_inputs_for_admin'

        get '/price_guess_inputs' => 'price_guesses#inputs_for_price_guess'
        post '/price_estimates' => 'price_guesses#create'
        get '/price_estimates/game_session/:game_session_id' => 'price_guesses#game_session_results'
        get '/price_estimates/game_session/:game_session_id/comparisons' => 'price_guesses#game_results'
        # below will eventually replace above
        get '/price_estimates/game_session/:game_session_id/game_result_calcs' => 'price_guesses#game_result_calcs'

        # Dossier price estimates CRUD - generated by augment:menmenmenmenmenmenmenmenmen
        get    '/dossiers/:realty_dossier_uuid/price_estimates'          => 'dossiers/price_estimates#index'
        post   '/dossiers/:realty_dossier_uuid/price_estimates'          => 'dossiers/price_estimates#create'
        get    '/dossiers/:realty_dossier_uuid/price_estimates/:uuid'    => 'dossiers/price_estimates#show'
        put    '/dossiers/:realty_dossier_uuid/price_estimates/:uuid'    => 'dossiers/price_estimates#update'
        patch  '/dossiers/:realty_dossier_uuid/price_estimates/:uuid'    => 'dossiers/price_estimates#update'
        delete '/dossiers/:realty_dossier_uuid/price_estimates/:uuid'    => 'dossiers/price_estimates#destroy'

        # /api_public/v4/forms
        post '/forms_ppsq/sp/game_property_feedback' => 'forms_ppsq#game_property_feedback'
        post '/forms_ppsq/sp/price_game_followup' => 'forms_ppsq#price_game_followup'
        post '/forms_ppsq/:form_id' => 'forms_ppsq#process_form'
        post '/forms_ppsq/:form_id/follow_up/:communication_uuid' => 'forms_ppsq#follow_up_form'
        post '/forms/:form_id' => 'htoc_forms#process_form'
        post '/forms/:form_id/follow_up/:communication_uuid' => 'htoc_forms#follow_up_form'
        post '/scoots/check/:sbdmn_name/:access_token' => 'scoots#check'
        get '/scoots/show/:sbdmn_name' => 'scoots#show'
        get '/scoots/show_games/:sbdmn_name' => 'scoots#show_games'
        #     // for viewing listing photos of a dossier asset - in case I need to run plan_b
        # should really have authentication on this
        get '/dossier_assets/show/:dossier_asset_id' => 'dossier_assets_comparisons#show_dossier_asset'
        get '/dossier_assets_comparisons/show/:comparison_uuid' => 'dossier_assets_comparisons#show'
        # 6 apr 2025 - above will replace below
        # get '/dossiers/show/:realty_dossier_uuid/compare/:secondary_asset_uuid' => 'dossiers#show_comparison'

        # below is for a screenshot that can be used for the LLM
        get '/dossiers/show_image/:realty_dossier_uuid/listing/:listing_uuid' => 'dossiers#show_screenshot_images'
        get '/dossiers/show_image/:realty_dossier_uuid/listing/:listing_uuid/discarded' => 'dossiers#show_discarded_screenshot_images'
        get '/dossiers/show_image/:realty_dossier_uuid/listing/:listing_uuid/plan_b' => 'dossiers#show_wrong_screenshot_images'
        # ###

        # 26 may 2025 - will soon be able to remove below
        # as now accessed via api_guestguegue
        get '/dossiers/show/:realty_dossier_uuid' => 'dossiers#show'
        # TODO: - secure below with auth
        get '/dossiers/list/superwiser' => 'dossiers#list_for_superwiser'
        # Dossier tasks CRUD
        get    '/dossiers/:realty_dossier_uuid/tasks'          => 'dossier_tasks#index'
        post   '/dossiers/:realty_dossier_uuid/tasks'          => 'dossier_tasks#create'
        get    '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#show'
        put    '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#update'
        patch  '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#update'
        delete '/dossiers/:realty_dossier_uuid/tasks/:uuid'    => 'dossier_tasks#destroy'

        get '/sale_listings/show/:sale_listing_uuid' => 'sale_listings#show'
        # TODO: - secure below with auth
        get '/sale_listings/list/superwiser' => 'sale_listings#list'

        resources :postcode_areas, only: [:index], defaults: { format: :json }
        get '/postcode_clusters/list/:outcode_id' => 'postcode_clusters#index', defaults: { format: :json }
        get '/postcode_clusters/show/:postcode_cluster_uuid' => 'postcode_clusters#show', defaults: { format: :json }
        # below so I can preview sample data to feed to an LLM
        get '/postcode_clusters/llm_view/:postcode_cluster_uuid' => 'postcode_clusters#llm_view', defaults: { format: :json }

        get '/realty_assets/detailed/:outcode_id' => 'realty_assets#detailed', defaults: { format: :json }

        get '/charts_data_examples/radar_chart', to: 'charts_data_examples#radar_chart_data'
        get '/charts_data_examples/box_plot', to: 'charts_data_examples#box_plot_data'
        get '/charts_data_examples/time_series', to: 'charts_data_examples#time_series_data'
        get '/charts_data_examples/scatter_plot', to: 'charts_data_examples#scatter_plot_data'
        get '/charts_data_examples/bar_chart', to: 'charts_data_examples#bar_chart_data'
        get '/charts_data_examples/heatmap', to: 'charts_data_examples#heatmap_data'

        get '/charts_data_tests/:data_source_name' => 'charts_data#charts_data_tests'

        # Feb 2025: this is the main one for the current front end
        get '/charts_data/:data_source_name' => 'charts_data#charts_data'
        # get '/charts_data/cn/:data_source_name' => 'charts_data#charts_data'
        get '/charts_data/cluster/:postcode_cluster_uuid/:data_source_name' => 'charts_data#charts_data_for_cluster'

        get '/charts/sold_data_scatter_charts' => 'charts#sold_data_scatter_charts'
        get '/charts/sold_data_column_charts' => 'charts#sold_data_column_charts'
        get '/charts/sold_data_line_charts' => 'charts#sold_data_line_charts'
        get '/charts/sold_data_pie_charts' => 'charts#sold_data_pie_charts'
        get '/charts/sold_data_bar_charts' => 'charts#sold_data_bar_charts'
        get '/charts/view/test' => 'charts#test_view'

        get '/sold_transactions/chart_view' => 'sold_transactions#chart_view'
        get '/sold_transactions/predictions/:outcode_id' => 'sold_transactions#predictions_for_outcode', defaults: { format: :json }
        get '/sold_transactions/full/:outcode_id' => 'sold_transactions#full_for_outcode', defaults: { format: :json }
        get '/sold_transactions/real' => 'sold_transactions#index_real' # , as: "single_page"
        get '/sold_transactions/real_for_cluster/:cluster_uuid' => 'sold_transactions#index_real_for_cluster' # , as: "single_page"
        get '/sold_transactions/synthetic' => 'sold_transactions#index_synthetic' # , as: "single_page"
        # get '/sold_transactions/:sold_transaction_id' => 'sold_transactions#show' # , as: "single_page"
        # resources :sold_transactions, only: %i[index show]

        get '/page/:page_slug' => 'pages#single_page' # , as: "single_page"
        # get '/component_data/:component_slug' => 'component_data#show'
        # get "/listings_data/:listings_grouping/:listing_slug" => "listings_data#show_listing"
        # get "/listings_data/:listings_grouping/:listing_uuid/custom" => "listings_data#show_custom_listing"
        # TODO - add route exposing public json data about listings
        # to make it easier to import to other sites
        # namespace :spp do
        #   get '/create_from_url' => 'spp#create_from_url'
        #   put '/create_from_html' => 'spp#create_from_html'
        #   get '/:listings_grouping/:listing_uuid' => 'spp#show'
        # end
        # namespace :ucp do
        #   get '/editor/extra_setup' => 'editor#extra_setup'
        #   get '/dashboard_setup/:uuid/:edit_token' => 'ucp#dashboard_setup'
        #   get '/create_for_user' => 'ucp#create_for_user'
        #   get '/:uuid/:edit_token/page_preview_html' => 'ucp#page_preview_html'
        #   get '/:uuid/:edit_token/page_edit_html' => 'ucp#page_edit_html'
        # end
        # put '/favourites' => 'favourites#create_or_update'
        # get "/search_simple/refresh_csrf_token" => "search_simple#refresh_csrf_token"
        # post '/forms/request_property_info/:listings_model_name/:listing_uuid' => 'forms#request_property_info'
        # post '/forms/request_general_info' => 'forms#request_general_info'

        # get '/search_simple/' \
        #   'results_only' \
        #   '/op/:op' \
        #   '/city/:city' \
        #   '/features_all/:features_all' \
        #   '/features_any/:features_any' \
        #   '/bedrooms_min/:bedrooms_min' \
        #   '/bedrooms_max/:bedrooms_max' \
        #   '/bathrooms_min/:bathrooms_min' \
        #   '/bathrooms_max/:bathrooms_max' \
        #   '/price_min/:price_min' \
        #   '/price_max/:price_max' \
        #   '/type/:type' \
        #   '/sort/:sort' \
        #   '/page_no/:page_no' \
        #   '/cs/:canned_search_uuid' => 'search_simple#results_only'
      end
    end
  end

  # authenticated :user, ->(u) { u.admin? } do
  # end
end
